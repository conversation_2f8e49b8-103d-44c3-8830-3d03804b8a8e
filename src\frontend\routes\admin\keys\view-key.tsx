import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  ArrowLeftIcon,
  CheckCircleIcon,
  ClockIcon,
  EyeIcon,
  EyeOffIcon,
  KeyIcon,
  RefreshCwIcon,
  ShieldCheckIcon,
} from "lucide-react";
import { useState } from "react";
import { Link, useParams } from "react-router";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Icons } from "@/components/ui/icons";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";

import { KeyActions } from "@/components/admin/key-actions";
import { KeyStatus } from "@/components/admin/key-status";

import { LLM_PROVIDER_DISPLAY_NAME } from "@/shared/providers";
import { format } from "@/shared/utils";

import { useTRPC } from "@/lib/trpc/client";
import type { Key } from "@/shared/key-management";

type KeyWithStats = Key & {
  stats?: any[];
};

function KeyDetailsSkeleton() {
  return (
    <div className="@container flex flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-6 w-px" />
          <div className="flex items-center gap-3">
            <Skeleton className="size-8 rounded" />
            <div>
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-9 w-24" />
          <Skeleton className="h-9 w-9" />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Skeleton className="h-64 w-full" />
        </div>
        <div className="space-y-6">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-48 w-full" />
        </div>
      </div>
    </div>
  );
}

function KeyOverviewCard({ keyData }: { keyData: KeyWithStats }) {
  return (
    <Card className="gap-4">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <KeyIcon className="h-5 w-5" />
          <span className="text-2xl">Key Information</span>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Key Hash */}
        <div className="flex flex-col gap-2">
          <span className="text-lg font-medium">Key Hash</span>
          <span className="block">{keyData.hash}</span>
        </div>

        {/* Provider */}
        <div className="flex flex-col gap-2">
          <span className="text-lg font-medium">Provider</span>

          <div className="flex items-center gap-2">
            <Icons.provider provider={keyData.provider} className="size-5" />
            <span className="font-medium">{LLM_PROVIDER_DISPLAY_NAME[keyData.provider]}</span>
          </div>
        </div>

        {/* Model IDs */}
        <div className="flex flex-col gap-2">
          <span className="text-lg font-medium">Configured Models ({keyData.modelIds.length})</span>

          {keyData.modelIds.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {keyData.modelIds.sort().map((modelId) => (
                <Badge key={modelId} variant="outline" className="rounded-md font-mono text-xs">
                  {modelId}
                </Badge>
              ))}
            </div>
          ) : (
            <div className="text-muted-foreground text-sm">No models configured</div>
          )}
        </div>

        {/* Metadata */}
        {Object.keys(keyData.metadata).length > 0 && (
          <div className="flex flex-col gap-2">
            <span className="text-lg font-medium">Metadata</span>
            <ul className="flex flex-col gap-2">
              {Object.entries(keyData.metadata).map(([key, value]) => (
                <li key={key} className="ml-4 list-disc">
                  <span className="font-medium capitalize">{key}:</span>{" "}
                  <code className="bg-muted/50 rounded-md border px-2 py-0.5 font-mono text-sm">
                    {value.toString()}
                  </code>
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function KeyStatusCard({ keyData }: { keyData: KeyWithStats }) {
  const pendingDeletion = keyData.metadata?.pendingDeletion as number | undefined;
  const disableReason = keyData.metadata?.disableReason as string | undefined;
  const isPendingDeletion = pendingDeletion && pendingDeletion > Date.now() / 1000;

  return (
    <Card className="gap-4">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShieldCheckIcon className="h-5 w-5" />
          Status & Health
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Current Status */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Current Status</label>
          <KeyStatus keyData={keyData} />
        </div>

        {/* Disable Reason */}
        {disableReason && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Disable Reason</label>
            <div className="bg-muted rounded-md p-3 text-sm">{disableReason}</div>
          </div>
        )}

        {/* Pending Deletion */}
        {isPendingDeletion && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Pending Deletion</label>
            <div className="bg-destructive/10 border-destructive/20 rounded-md border p-3">
              <div className="text-destructive flex items-center gap-2 text-sm">
                <ClockIcon className="h-4 w-4" />
                <span>Scheduled for deletion on {format.date(pendingDeletion * 1000)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Rate Limiting */}
        {keyData.ratelimitedUntil > Date.now() / 1000 && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Rate Limited</label>
            <div className="rounded-md border border-yellow-200 bg-yellow-50 p-3">
              <div className="flex items-center gap-2 text-sm text-yellow-800">
                <ClockIcon className="h-4 w-4" />
                <span>Rate limited until {format.date(keyData.ratelimitedUntil * 1000)}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function KeyTimestampsCard({ keyData }: { keyData: KeyWithStats }) {
  const formatTimestamp = (timestamp: number) => {
    if (timestamp === 0) return "Never";
    return format.date(timestamp * 1000);
  };

  const formatRelativeTime = (timestamp: number) => {
    if (timestamp === 0) return "";
    const now = Date.now();
    const diff = now - timestamp * 1000;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    return "Recently";
  };

  return (
    <Card className="gap-4">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ClockIcon className="h-5 w-5" />
          Timestamps
        </CardTitle>
      </CardHeader>

      <CardContent className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        {/* Created At */}
        <div className="space-y-1">
          <label className="text-sm font-medium">Created</label>
          <div className="text-sm">
            <div>{formatTimestamp(keyData.createdAt)}</div>
            <div className="text-muted-foreground text-xs">
              {formatRelativeTime(keyData.createdAt)}
            </div>
          </div>
        </div>

        {/* Last Used */}
        <div className="space-y-1">
          <label className="text-sm font-medium">Last Used</label>
          <div className="text-sm">
            <div>{formatTimestamp(keyData.lastUsedAt / 1000)}</div>

            {keyData.lastUsedAt > 0 && (
              <div className="text-muted-foreground text-xs">
                {formatRelativeTime(keyData.lastUsedAt / 1000)}
              </div>
            )}
          </div>
        </div>

        {/* Last Checked */}
        <div className="space-y-1">
          <label className="text-sm font-medium">Last Checked</label>
          <div className="text-sm">
            <div>{formatTimestamp(keyData.lastCheckedAt / 1000)}</div>
            {keyData.lastCheckedAt > 0 && (
              <div className="text-muted-foreground text-xs">
                {formatRelativeTime(keyData.lastCheckedAt / 1000)}
              </div>
            )}
          </div>
        </div>

        {/* Next Check */}
        {keyData.nextCheckAt && (
          <div className="space-y-1">
            <label className="text-sm font-medium">Next Check</label>
            <div className="text-sm">
              <div>{formatTimestamp(keyData.nextCheckAt / 1000)}</div>
              <div className="text-muted-foreground text-xs">
                {keyData.nextCheckAt / 1000 > Date.now() ? "Scheduled" : "Overdue"}
              </div>
            </div>
          </div>
        )}

        {/* Updated At */}
        <div className="space-y-1">
          <label className="text-sm font-medium">Last Updated</label>
          <div className="text-sm">
            <div>{formatTimestamp(keyData.updatedAt)}</div>
            <div className="text-muted-foreground text-xs">
              {formatRelativeTime(keyData.updatedAt)}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function KeyUsageStatsCard({ stats }: { stats: any[] }) {
  if (!stats || stats.length === 0) return null;

  const totalInputTokens = stats.reduce((sum, stat) => sum + (stat.inputTokens || 0), 0);
  const totalOutputTokens = stats.reduce((sum, stat) => sum + (stat.outputTokens || 0), 0);
  const totalReasoningTokens = stats.reduce((sum, stat) => sum + (stat.reasoningTokens || 0), 0);
  const totalPrompts = stats.reduce((sum, stat) => sum + (stat.prompts || 0), 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircleIcon className="h-5 w-5" />
          Usage Statistics
        </CardTitle>
      </CardHeader>

      <CardContent>
        {/* Summary Stats */}
        <div className="mb-6 grid grid-cols-2 gap-4 md:grid-cols-4">
          <div className="space-y-1">
            <p className="text-muted-foreground text-xs tracking-wide uppercase">Total Prompts</p>
            <p className="text-2xl font-bold">{format.number(totalPrompts)}</p>
          </div>

          <div className="space-y-1">
            <p className="text-muted-foreground text-xs tracking-wide uppercase">Input Tokens</p>
            <p className="text-2xl font-bold">{format.number(totalInputTokens)}</p>
          </div>

          <div className="space-y-1">
            <p className="text-muted-foreground text-xs tracking-wide uppercase">Output Tokens</p>
            <p className="text-2xl font-bold">{format.number(totalOutputTokens)}</p>
          </div>

          {totalReasoningTokens > 0 && (
            <div className="space-y-1">
              <p className="text-muted-foreground text-xs tracking-wide uppercase">
                Reasoning Tokens
              </p>
              <p className="text-2xl font-bold">{format.number(totalReasoningTokens)}</p>
            </div>
          )}
        </div>

        {/* Per Model Family Breakdown */}
        <div className="space-y-4">
          <h4 className="font-medium">Usage by Model Family</h4>
          <div className="space-y-3">
            {stats.map((stat) => (
              <div key={stat.id} className="rounded-lg border p-4">
                <div className="mb-3 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="font-mono text-xs">
                      {stat.modelFamily?.name || stat.modelFamily}
                    </Badge>

                    {stat.modelFamily?.provider && (
                      <Icons.provider provider={stat.modelFamily.provider} className="size-4" />
                    )}
                  </div>

                  <div className="text-muted-foreground text-sm">
                    {format.number(stat.prompts)} prompts
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
                  <div className="space-y-1">
                    <p className="text-muted-foreground text-xs">Input Tokens</p>
                    <p className="font-medium">{format.number(stat.inputTokens)}</p>
                  </div>

                  <div className="space-y-1">
                    <p className="text-muted-foreground text-xs">Output Tokens</p>
                    <p className="font-medium">{format.number(stat.outputTokens)}</p>
                  </div>

                  {stat.reasoningTokens > 0 && (
                    <div className="space-y-1">
                      <p className="text-muted-foreground text-xs">Reasoning Tokens</p>
                      <p className="font-medium">{format.number(stat.reasoningTokens)}</p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function AdminViewKeyPage() {
  const { hash } = useParams<{ hash: string }>();

  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const {
    data: keyData,
    isLoading,
    error,
    refetch: refetchKey,
  } = useQuery({
    ...trpc.keys.getByHash.queryOptions({ hash: hash! }),
    enabled: !!hash,
  });

  const { mutate: recheckKey, isPending: isRechecking } = useMutation({
    ...trpc.keys.recheckKey.mutationOptions(),
    onSuccess: () => {
      toast.success("Key recheck initiated");
      refetchKey();
    },
    onError: (error) => {
      toast.error(`Failed to recheck key: ${error.message}`);
    },
  });

  if (!hash) {
    return (
      <div className="flex h-24 items-center justify-center">
        <div className="text-muted-foreground">Invalid key hash</div>
      </div>
    );
  }

  if (isLoading) return <KeyDetailsSkeleton />;

  if (error || !keyData) {
    return (
      <div className="flex h-24 items-center justify-center">
        <div className="text-muted-foreground">{error?.message || "Key not found"}</div>
      </div>
    );
  }

  const handleRecheck = () => {
    recheckKey({ hash });
  };

  return (
    <div className="@container flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <Icons.provider provider={keyData.provider} className="size-8" />
            <div>
              <h1 className="text-2xl font-bold">API Key Details</h1>
              <p className="text-muted-foreground">
                {LLM_PROVIDER_DISPLAY_NAME[keyData.provider]} Key
              </p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRecheck}
            disabled={isRechecking || keyData.status === "revoked" || keyData.status === "disabled"}
          >
            <RefreshCwIcon className={`mr-2 h-4 w-4 ${isRechecking ? "animate-spin" : ""}`} />
            {isRechecking ? "Rechecking..." : "Recheck"}
          </Button>

          <Button variant="outline" size="sm" onClick={() => refetchKey()} disabled={isLoading}>
            <RefreshCwIcon className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
            <span className="sr-only">{isLoading ? "Refreshing..." : "Refresh"}</span>
          </Button>

          <KeyActions keyData={keyData} refetch={refetchKey} />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Main Key Information */}
        <div className="lg:col-span-2">
          <KeyOverviewCard keyData={keyData} />
        </div>

        {/* Status and Actions */}
        <div className="space-y-6">
          <KeyStatusCard keyData={keyData} />
          <KeyTimestampsCard keyData={keyData} />
        </div>
      </div>

      {/* Usage Statistics */}
      {keyData.stats && keyData.stats.length > 0 && <KeyUsageStatsCard stats={keyData.stats} />}
    </div>
  );
}
