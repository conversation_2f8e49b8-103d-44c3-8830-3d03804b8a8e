import type { Context } from "hono";
import { z } from "zod/v4";

import { config } from "@/config";

// https://platform.openai.com/docs/api-reference/chat/create
const OpenAIV1ChatContentArraySchema = z.array(
  z.discriminatedUnion("type", [
    z.object({
      type: z.literal("text"),
      text: z.string(),
    }),
    z.object({
      type: z.literal(["image", "image_url"]),
      image_url: z.object({
        url: z.string(),
        detail: z.enum(["low", "auto", "high"]).default("auto"),
      }),
    }),
  ]),
);

export const OpenAIToolsSchema = z.object({
  type: z.literal("function"),
  function: z.object({
    name: z
      .string()
      .regex(/[a-zA-Z0-9_]/g, {
        message: "Function name must only contain alphanumeric characters and underscores.",
      })
      .max(64, { message: "Function name must be less than 64 characters." }),

    description: z.string().optional(),
    strict: z.boolean().nullish(),
    parameters: z.record(z.string(), z.unknown()).optional(),
  }),
});

export const OpenAIToolChoiceSchema = z
  .union([
    z.enum(["none", "auto", "required"]),
    z.object({ function: z.object({ name: z.string() }), type: z.literal("function") }),
  ])
  .optional();

export const OpenAIV1ChatCompletionSchema = z.object({
  model: z.string(),
  messages: z.array(
    z.object({
      role: z.enum(["developer", "system", "user", "assistant", "tool", "function"]),
      content: z.union([z.string(), OpenAIV1ChatContentArraySchema]),
      name: z.string().optional(),
      tool_calls: z.array(z.any()).optional(),
      function_call: z.array(z.any()).optional(),
      tool_call_id: z.string().optional(),
      refusal: z.string().nullish(),
    }),
    {
      error: (issue) =>
        issue.input === undefined
          ? "No `messages` found. Ensure you've set the correct completion endpoint."
          : "Messages were not formatted correctly. Refer to the OpenAI Chat API documentation for more information.",
    },
  ),
  temperature: z.number().optional(),
  top_p: z.number().optional(),
  n: z.literal(1, { message: "You may only request a single completion at a time." }).optional(),
  stream: z.boolean().optional().default(false),
  stream_options: z.object({ include_usage: z.boolean().optional() }).nullish(),
  stop: z.union([z.string(), z.array(z.string())]).nullish(),
  store: z
    .boolean()
    .nullish()
    .overwrite((v) => (typeof v === "boolean" ? false : v)),

  /**
   * @deprecated use `max_completion_tokens` instead.
   */
  max_tokens: z.coerce.number().int().nullish(),
  max_completion_tokens: z.coerce.number().int().nullish(),

  frequency_penalty: z.number().min(-2).max(2).optional(),
  presence_penalty: z.number().min(-2).max(2).optional(),
  logit_bias: z.any().optional(),

  seed: z.number().int().optional(),

  logprobs: z.boolean().optional(),
  top_logprobs: z.number().int().optional(),

  reasoning_effort: z.enum(["low", "medium", "high"]).optional(),
  response_format: z.record(z.string(), z.unknown()).optional(),
  service_tier: z.string().nullish(),

  // Quickly adding some newer tool usage params
  tools: z.array(OpenAIToolsSchema).optional(),
  functions: z.array(OpenAIToolsSchema).optional(),
  tool_choice: OpenAIToolChoiceSchema.optional(),
  function_choice: OpenAIToolChoiceSchema.optional(),

  parallel_tool_calls: z.boolean().optional(),

  web_search_options: z
    .object({
      search_context_size: z.enum(["low", "high", "medium"]).optional(),
      user_location: z
        .object({
          type: z.literal("approximate"),
          approximate: z.record(z.string(), z.unknown()).optional(),
        })
        .nullish(),
    })
    .optional(),
});

export function getOpenAISchema(ctx: Context) {
  return OpenAIV1ChatCompletionSchema.transform((data) => {
    if (!config.allowToolUsage.includes(ctx.var.service)) {
      delete data.tools;
      delete data.tool_choice;
      delete data.functions;
      delete data.function_choice;
    }

    if (
      !data.model.startsWith("o1") ||
      !data.model.startsWith("o3") ||
      !data.model.startsWith("o4") ||
      !data.model.startsWith("codex-")
    ) {
      delete data.reasoning_effort;
    }

    const max_completion_tokens =
      data.max_completion_tokens ?? data.max_tokens ?? Number.MAX_SAFE_INTEGER;

    // Should use max_completion_tokens instead of max_tokens as max_tokens is deprecated.
    // See: https://platform.openai.com/docs/api-reference/chat/create#chat-create-max_tokens
    delete data.max_tokens;
    data.max_completion_tokens = max_completion_tokens;

    if (!data.stream) delete data.stream_options;

    return data;
  });
}
