import type { z } from "zod";

import type { Context } from "hono";
import { proxy } from "hono/proxy";

import { decrypt } from "@/shared/key-management/utils";
import { type LLM_Providers } from "@/shared/providers";

import { createHeaders } from "./headers";
import { validateModelFamily } from "./requests/proxy/validate-model-family";
import { tryCatch } from "@/shared/utils/try-catch";

export function createProxy<T extends z.ZodType>(data: {
  provider: LLM_Providers;
  basePath: string;
  validateSchema: (ctx: Context) => T;
}) {
  return async (ctx: Context) => {
    const body = await ctx.req.json();
    ctx.req.bodyCache.json = body;

    const requestData: Context["var"]["requestData"] = {
      model: ctx.req.bodyCache.json.model,
      streaming: ctx.req.bodyCache.json.stream,
      provider: data.provider,
    };

    if (data.provider === "gemini") {
      const [model, method] = ctx.req.param("path").split(":") as [string, string];
      requestData.model = model;
      requestData.streaming = method === "streamGenerateContent";
    }

    ctx.set("requestData", requestData);

    const [, err] = await tryCatch(() => validateModelFamily(ctx));
    if (err) {
      ctx.var.logger.warn({ err }, "Failed to validate model family");
      return Response.json({ error: { message: err.message } }, { status: 400 });
    }

    const { provider, basePath } = data;
    const parsedBody = data.validateSchema(ctx).safeParse(body);

    if (!parsedBody.success) {
      ctx.var.logger.warn({ issues: parsedBody.error.issues }, "Invalid request");
      return Response.json(
        { error: { message: "Invalid request", issues: parsedBody.error.issues } },
        { status: 400 },
      );
    }

    const forceSSE = provider === "gemini" && requestData.streaming ? "?alt=sse" : "";
    const url = `${basePath}${ctx.req.path.replace(`/proxy/${provider}`, "")}${forceSSE}`;

    const key = await ctx.var.keyPool.get(requestData.model, data.provider);
    requestData.selectedKey = key;

    const headers = createHeaders({
      provider,
      headers: ctx.req.raw.headers,
      extra: { "Content-Type": "application/json" },
      key: decrypt(requestData.selectedKey.encryptedKey),
    });

    ctx.var.logger.info({ from: ctx.req.url, to: url }, "Sending request to upstream API...");

    const res = await proxy(url, {
      method: ctx.req.method,
      body: JSON.stringify(parsedBody.data),
      headers,
    });

    return new Response(res.body, {
      headers: res.headers,
      status: res.status,
      statusText: res.statusText,
    });
  };
}
