import { useMutation, useQueryClient } from "@tanstack/react-query";
import { EditIcon, MoreHorizontalIcon, TrashIcon } from "lucide-react";
import { memo, useCallback, useState } from "react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Icons } from "@/components/ui/icons";

import type { schema } from "@/shared/database";
import { LLM_PROVIDER_DISPLAY_NAME } from "@/shared/providers";
import { format } from "@/shared/utils";

import { useTRPC } from "@/lib/trpc/client";

type ModelFamily = typeof schema.modelFamilies.$inferSelect;
type ModelFamilyCardProps = {
  family: ModelFamily;
};

// Memoized header component to prevent unnecessary re-renders
const ModelFamilyHeader = memo(
  ({
    family,
    onToggleStatus,
    onDelete,
    isUpdating,
  }: {
    family: ModelFamily;
    onToggleStatus: () => void;
    onDelete: () => void;
    isUpdating: boolean;
  }) => {
    return (
      <CardHeader className="pb-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Icons.provider provider={family.provider} className="size-10" />

              <div>
                <h3 className="text-lg font-semibold">{family.name}</h3>
                <p className="text-muted-foreground text-sm">
                  {LLM_PROVIDER_DISPLAY_NAME[family.provider]}
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              data-status={family.status}
              onClick={onToggleStatus}
              disabled={isUpdating}
              size="sm"
              variant="ghost"
              className="gap-2"
            >
              <div
                data-status={family.status}
                className="size-3 rounded-full data-[status=disabled]:bg-red-500 data-[status=enabled]:bg-green-500"
              />
              <span className="text-sm font-medium capitalize">{family.status}</span>
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontalIcon className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>

              <DropdownMenuContent align="end">
                <DropdownMenuItem disabled>
                  <EditIcon className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>

                <DropdownMenuItem onClick={onDelete} disabled={isUpdating} className="text-red-600">
                  <TrashIcon className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
    );
  },
);
ModelFamilyHeader.displayName = "ModelFamilyHeader";

// Memoized stats component for cost and limits display
const ModelFamilyStats = memo(({ family }: { family: ModelFamily }) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      {/* Input Cost */}
      <div className="space-y-1">
        <p className="text-muted-foreground text-xs tracking-wide uppercase">Input Cost</p>
        <p className="font-semibold">{format.cost(family.inputCost)}</p>
      </div>

      {/* Output Cost */}
      <div className="space-y-1">
        <p className="text-muted-foreground text-xs tracking-wide uppercase">Output Cost</p>
        <p className="font-semibold">{format.cost(family.outputCost)}</p>
      </div>

      {/* Max Context */}
      <div className="space-y-1">
        <p className="text-muted-foreground text-xs tracking-wide uppercase">Max Context</p>
        <p className="font-semibold">{format.number(family.maxContext)}</p>
      </div>

      {/* Max Output */}
      <div className="space-y-1">
        <p className="text-muted-foreground text-xs tracking-wide uppercase">Max Output</p>
        <p className="font-semibold">{format.number(family.maxOutput)}</p>
      </div>
    </div>
  );
});
ModelFamilyStats.displayName = "ModelFamilyStats";

// Memoized models list component with its own state
const ModelsList = memo(({ family }: { family: ModelFamily }) => {
  const [showModels, setShowModels] = useState(false);

  return (
    <div className="w-full border-t pt-4">
      <div className="flex items-start justify-between">
        <div className="text-muted-foreground text-xs tracking-wide uppercase">
          Configured Model IDs ({family.models.length}):
        </div>

        <div className="flex items-center gap-2">
          <span className="text-muted-foreground text-sm">Rate limit cost:</span>
          <span className="font-medium">{family.ratelimitCost}</span>
        </div>
      </div>

      <div className="flex flex-wrap gap-1">
        {family.models.map((modelId) => (
          <Badge key={modelId} variant="outline" className="rounded-md font-mono text-xs">
            {modelId}
          </Badge>
        ))}
      </div>

      {/* No models configured message */}
      {family.models.length === 0 && (
        <div className="text-muted-foreground text-xs italic">
          No models configured for this family
        </div>
      )}
    </div>
  );
});
ModelsList.displayName = "ModelsList";

// Memoized delete dialog component
const DeleteConfirmationDialog = memo(
  ({
    open,
    onOpenChange,
    onConfirm,
    familyName,
    isDeleting,
  }: {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onConfirm: () => void;
    familyName: string;
    isDeleting: boolean;
  }) => (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Model Family</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete "{familyName}"? This action cannot be undone and will
            permanently remove this model family and all its configurations.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  ),
);
DeleteConfirmationDialog.displayName = "DeleteConfirmationDialog";

export function ModelFamilyCard({ family }: ModelFamilyCardProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const queryClient = useQueryClient();
  const trpc = useTRPC();

  const { mutateAsync: updateModelFamily } = useMutation(trpc.models.update.mutationOptions());
  const { mutateAsync: deleteModelFamily } = useMutation(trpc.models.delete.mutationOptions());

  const handleToggleStatus = useCallback(async () => {
    setIsUpdating(true);
    try {
      const newStatus = family.status === "enabled" ? "disabled" : "enabled";
      await updateModelFamily({ id: family.id, status: newStatus });

      queryClient.invalidateQueries({ queryKey: trpc.models.list.queryKey() });
    } catch (error) {
      console.error("Failed to update status:", error);
    }

    setIsUpdating(false);
  }, [family.id, family.status, updateModelFamily, queryClient]);

  const handleDeleteClick = useCallback(() => {
    setShowDeleteDialog(true);
  }, []);

  const handleDeleteConfirm = useCallback(async () => {
    setIsUpdating(true);
    try {
      await deleteModelFamily({ id: family.id });
      queryClient.invalidateQueries({ queryKey: trpc.models.list.queryKey() });
      setShowDeleteDialog(false);
    } catch (error) {
      console.error("Failed to delete family:", error);
    }

    setIsUpdating(false);
  }, [family.id, deleteModelFamily, queryClient]);

  const handleDialogOpenChange = useCallback((open: boolean) => {
    setShowDeleteDialog(open);
  }, []);

  return (
    <Card className="gap-3 transition-shadow hover:shadow-md">
      <ModelFamilyHeader
        family={family}
        onToggleStatus={handleToggleStatus}
        onDelete={handleDeleteClick}
        isUpdating={isUpdating}
      />

      <CardContent className="pt-0">
        <ModelFamilyStats family={family} />
      </CardContent>

      <CardFooter className="w-full">
        <ModelsList family={family} />
      </CardFooter>

      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={handleDialogOpenChange}
        onConfirm={handleDeleteConfirm}
        familyName={family.name}
        isDeleting={isUpdating}
      />
    </Card>
  );
}
