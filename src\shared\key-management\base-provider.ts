import crypto from "node:crypto";

import { type Key, type Key<PERSON><PERSON><PERSON> } from "@/shared/key-management";
import { encrypt, prioritizeKeys } from "@/shared/key-management/utils";

import { db, schema, sql } from "@/shared/database";

import { LLM_PROVIDER_DISPLAY_NAME, type LLM_Providers } from "../providers";
import type { KeyCheckerBase } from "./base-checker";

import { config } from "@/config";
import { logger } from "@/logger";

/**
 * Abstract base key provider encapsulating shared logic and a shared execute() flow.
 * Concrete providers should implement provider-specific hooks.
 */
export abstract class BaseKeyProvider<TKey extends Key = Key> implements KeyProvider<TKey> {
  static readonly provider: LLM_Providers;
  readonly provider: LLM_Providers;
  readonly logger;
  private checker?: KeyCheckerBase<TKey>;

  constructor(provider: LLM_Providers) {
    this.provider = provider;
    this.logger = logger.child({ module: `key-provider-${provider}` });
  }

  /**
   * Sync local config keys into database.
   */
  async init({ checker }: { checker: KeyCheckerBase<TKey> }): Promise<void> {
    let newAdded = 0;
    for (const key of this.getSeedKeysFromConfig()) {
      const success = await this.addKey(key);
      if (success) newAdded++;
    }

    if (newAdded) this.logger.info({ newAdded }, "Added new keys from config.");

    const keysCount = await db.$count(schema.keys, sql`provider = ${this.provider}`);
    this.logger.info(
      { keysCount, newAdded: newAdded > 0 ? newAdded : undefined },
      `Loaded ${LLM_PROVIDER_DISPLAY_NAME[this.provider]} keys.`,
    );

    // Start the checker after initial sync
    if (config.checkKeys) {
      this.checker = checker;
      this.checker.start();
    }
  }

  /**
   * Provider must return a list of seed plaintext keys from configuration.
   * Example: config.keys.openai
   */
  protected abstract getSeedKeysFromConfig(): string[];

  /**
   * Provider may customize metadata for an inserted key.
   */
  protected serializeNewKeyMetadataForInsert(): Key["metadata"] {
    return {};
  }

  /**
   * Providers MUST override to supply a short, provider-specific prefix used in hashes.
   * Example: "oai" for OpenAI. Total hash length (including prefix and dash) is limited to 32.
   */
  protected getProviderPrefix(): string {
    throw new Error(`Provider ${this.provider} must implement getProviderPrefix()`);
  }

  /**
   * Provider can customize model matching logic.
   */
  protected matchesModel(key: { modelIds: string[] }, modelId: string): boolean {
    if (!key.modelIds || (key.modelIds.length > 0 && !key.modelIds.includes(modelId))) return false;
    return true;
  }

  /**
   * Provider can customize how to bucket stats model family.
   */
  protected buildUsageStatsModelFamily(modelId: string): string {
    return modelId;
  }

  /**
   * Compute hash of plaintext API key for dedup/logging. Never log plaintext.
   */
  protected computeHash(plainKey: string): string {
    // Provider-specific, length-limited hash: "<prefix>-<truncated-hex>"
    // Limit total length to 32 chars including prefix.
    const prefix = this.getProviderPrefix();
    if (!prefix || typeof prefix !== "string") {
      throw new Error(
        `Provider ${this.provider} did not return a valid prefix from getProviderPrefix()`,
      );
    }

    const rawHex = crypto.createHash("sha256").update(plainKey).digest("hex");
    // Reserve 1 for the dash, so payload length = 32 - prefix.length - 1
    const payloadLen = Math.max(0, 32 - prefix.length - 1);
    if (payloadLen <= 0) {
      throw new Error(`Provider prefix "${prefix}" is too long; cannot construct 32-char hash`);
    }

    const truncated = rawHex.slice(0, payloadLen);
    const composed = `${prefix}-${truncated}`;

    // Safety: ensure max length 32
    return composed.length > 32 ? composed.slice(0, 32) : composed;
  }

  async addKey(key: string, defaultData?: Partial<TKey>): Promise<boolean> {
    const plain = key.trim();
    if (!plain) return false;

    const hash = defaultData?.hash ?? this.computeHash(plain);
    // Dedup by hash+provider to avoid cross-provider collisions
    const exists = await db.query.keys.findFirst({
      where: sql`hash = ${hash} AND provider = ${this.provider}`,
    });

    if (exists) return false;

    const encrypted = encrypt(plain);
    const metadata = this.serializeNewKeyMetadataForInsert();

    await db.insert(schema.keys).values({
      hash,
      encryptedKey: encrypted,
      provider: this.provider,
      modelIds: [],
      status: "unknown",
      disabledAt: null,
      disabledBy: null,
      ratelimitedAt: 0,
      ratelimitedUntil: 0,
      metadata,
      ...defaultData,
    });

    return true;
  }

  async removeKey(hash: string): Promise<boolean> {
    const res = await db
      .delete(schema.keys)
      .where(sql`hash = ${hash} AND provider = ${this.provider}`)
      .returning();

    return res.length > 0;
  }

  async get(modelId: string, customComparator?: (a: TKey, b: TKey) => number): Promise<TKey> {
    const rows = await db
      .select()
      .from(schema.keys)
      .where(sql`provider = ${this.provider} AND status = 'working'`);

    const candidates = rows.filter((k) => this.matchesModel(k, modelId)) as TKey[];

    if (candidates.length === 0) {
      throw new Error(`No available ${this.provider} keys for model ${modelId}`);
    }

    const keysByPriority = prioritizeKeys(candidates, customComparator);
    const selected = keysByPriority[0]!;

    await db
      .update(schema.keys)
      .set({ lastUsedAt: Date.now() })
      .where(sql`hash = ${selected.hash} AND provider = ${this.provider}`);

    return selected;
  }

  async list(): Promise<TKey[]> {
    const keys = await db.query.keys.findMany({
      where: sql`provider = ${this.provider}`,
    });

    return keys as unknown as TKey[];
  }

  async update(hash: string, update: Partial<TKey>): Promise<void> {
    await db
      .update(schema.keys)
      .set(update)
      .where(sql`hash = ${hash}`);
  }

  async available(modelId?: string): Promise<number> {
    const where = sql`provider = ${this.provider} AND status != 'disabled' AND status != 'revoked'`;
    const data = await db.query.keys.findMany({ where });

    if (modelId) {
      return data.filter((k) => this.matchesModel(k, modelId)).length;
    }

    return data.length;
  }

  async incrementUsage(
    hash: string,
    data: {
      modelFamily: string;
      inputTokens: number;
      outputTokens: number;
      reasoningTokens?: number;
    },
  ): Promise<void> {
    throw new Error("Not implemented");
  }

  async markRateLimited(hash: string): Promise<void> {
    const nowSec = Date.now();
    await db
      .update(schema.keys)
      .set({ status: "ratelimited", ratelimitedAt: nowSec, ratelimitedUntil: nowSec })
      .where(sql`hash = ${hash} AND provider = ${this.provider}`);
  }

  async recheck(): Promise<void> {
    await this.checker?.scheduleNextRun();
  }

  async recheckSingleKey(hash: string): Promise<void> {
    await this.checker?.recheckSingleKey(hash);
  }
}
