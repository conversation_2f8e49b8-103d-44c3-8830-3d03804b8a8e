import { tryCatch } from "@/shared/utils/try-catch";

import { Key<PERSON>heckerBase } from "@/shared/key-management/base-checker";
import { CheckFailedError } from "@/shared/key-management/error";
import { decrypt } from "@/shared/key-management/utils";

import { type OpenAIKey, OpenAIKeyProvider } from "./provider";

const POST_CHAT_COMPLETIONS_URL = "https://api.openai.com/v1/chat/completions";
const GET_MODELS_URL = "https://api.openai.com/v1/models";
const GET_ORGANIZATIONS_URL = "https://api.openai.com/v1/me";

const MIN_CHECK_INTERVAL = 3 * 1000; // 3 seconds
const KEY_CHECK_PERIOD = 60 * 60 * 1000; // 1 hour

type GetModelsResponse = {
  data: [{ id: string }];
};

type GetOrganizationsResponse = {
  orgs: { data: [{ id: string; is_default: boolean }] };
};

type OpenAIError = {
  error: { type: string; code: string; param: unknown; message: string };
};

export class OpenAI<PERSON>ey<PERSON>hecker extends KeyCheckerBase<OpenAIKey> {
  private cloneKeyForOrganizations: typeof OpenAIKeyProvider.prototype.cloneKeyForOrganizations;

  constructor({
    updateFn,
    cloneKeyForOrganizations,
  }: {
    updateFn: typeof OpenAIKeyProvider.prototype.update;
    cloneKeyForOrganizations: typeof OpenAIKeyProvider.prototype.cloneKeyForOrganizations;
  }) {
    super({
      updateFn,
      provider: OpenAIKeyProvider.provider,
      keyCheckPeriod: KEY_CHECK_PERIOD,
      minCheckInterval: MIN_CHECK_INTERVAL,
    });

    this.cloneKeyForOrganizations = cloneKeyForOrganizations;
  }

  protected override createHeaders(key: OpenAIKey): Headers {
    const headers = new Headers();
    headers.set("Authorization", `Bearer ${decrypt(key.encryptedKey)}`);

    if (key.metadata.organizationId) {
      headers.set("OpenAI-Organization", key.metadata.organizationId);
    }
    return headers;
  }

  private detectTier(headers: Headers): OpenAIKey["metadata"]["tier"] {
    const tokensLimit = Number(headers.get("x-ratelimit-limit-tokens"));

    switch (true) {
      case tokensLimit <= 30_000:
        return "tier-1";
      case tokensLimit <= 450_000:
        return "tier-2";
      case tokensLimit <= 800_000:
        return "tier-3";
      case tokensLimit <= 2_000_000:
        return "tier-4";
      case tokensLimit <= 30_000_000:
        return "tier-5";
      default:
        return "unknown";
    }
  }

  private async getModelIds(key: OpenAIKey): Promise<string[]> {
    const response = await fetch(GET_MODELS_URL, {
      headers: this.createHeaders(key),
    });
    if (!response.ok) {
      throw new CheckFailedError("Failed to get models", response.status, response);
    }

    const [data, err] = await tryCatch(() => response.json() as Promise<GetModelsResponse>);
    if (err) {
      throw new CheckFailedError("Failed to parse models", response.status, response);
    }

    const modelIds = new Set<string>(data.data.map(({ id }) => id));
    return Array.from(modelIds);
  }

  private async testLiveness(key: OpenAIKey) {
    const payload = {
      model: "gpt-4o",
      max_completion_tokens: -1,
      messages: [{ role: "user", content: "" }],
    };

    const response = await fetch(POST_CHAT_COMPLETIONS_URL, {
      method: "POST",
      headers: this.createHeaders(key),
      body: JSON.stringify(payload),
    });

    if (response.status !== 400) {
      throw new CheckFailedError(
        "Incorrect status code for checking liveness",
        response.status,
        response,
      );
    }

    const [data, error] = await tryCatch(response.json() as Promise<OpenAIError>);
    if (error) {
      throw new CheckFailedError("Failed to parse error response", response.status, response);
    }

    if (data.error.type !== "invalid_request_error") {
      this.logger.warn(
        { key: key.hash, error: data },
        "Unexpected 400 error class while checking key; assuming key is valid, but this may indicate a change in the API.",
      );
    }

    return { tier: this.detectTier(response.headers) };
  }

  private async testVerification(key: OpenAIKey): Promise<boolean> {
    const payload = {
      model: "o3",
      stream: true,
      max_completion_tokens: 1,
      messages: [{ role: "user", content: "" }],
    };

    const response = await fetch(POST_CHAT_COMPLETIONS_URL, {
      method: "POST",
      headers: this.createHeaders(key),
      body: JSON.stringify(payload),
    });

    return response.ok;
  }

  private async getOrganizations(
    key: OpenAIKey,
  ): Promise<{ orgId: string; orgs: string[] } | null> {
    if (key.metadata.organizationId) return null;

    try {
      const response = await fetch(GET_ORGANIZATIONS_URL, {
        headers: this.createHeaders(key),
      });

      const [data, error] = await tryCatch(response.json() as Promise<GetOrganizationsResponse>);
      if (error) {
        throw new CheckFailedError("Failed to parse organizations", response.status, response);
      }

      const organizations = data.orgs.data;

      const defaultOrg = organizations.find(({ is_default }) => is_default)!;
      const ids = organizations.filter(({ is_default }) => !is_default).map(({ id }) => id);

      if (organizations.length <= 1) return { orgId: defaultOrg.id, orgs: [] };

      this.logger.info(
        { parent: key.hash, defaultOrg: defaultOrg.id, organizations: ids },
        "Key is associated with multiple organizations; cloning key for each organization.",
      );

      return { orgId: defaultOrg.id, orgs: ids };
    } catch (error) {
      let info: string | Record<string, unknown>;
      const expectedErrorCodes = ["invalid_api_key", "no_organization"];

      if (error instanceof CheckFailedError) {
        const data = (await error.response.json()) as OpenAIError;
        if (expectedErrorCodes.includes(data.error.code)) return null;
        else info = { status: error.status, data };
      } else {
        info = (error as Error).message;
      }

      this.logger.warn({ parent: key.hash, error: info }, "Failed to fetch organizations for key.");
      return null;
    }
  }

  protected override async testKeyOrFail(key: OpenAIKey) {
    const requireFullCheck = key.status === "unknown";
    const requireModelCheck =
      key.lastModelsCheckedAt < Date.now() - 24 * 60 * 60 * 1000 && !requireFullCheck;

    const updates: Partial<OpenAIKey> = {};

    if (requireFullCheck) {
      const [{ tier }, modelIds, verified, orgs] = await Promise.all([
        this.testLiveness(key),
        this.getModelIds(key),
        this.testVerification(key),
        this.getOrganizations(key),
      ]);

      updates.status = "working";
      updates.metadata = { ...key.metadata, tier, verified };
      updates.modelIds = modelIds.filter((id) => !id.includes("ft")).sort();

      updates.lastModelsCheckedAt = Date.now();

      if (orgs) {
        updates.metadata.organizationId = orgs.orgId;
        await this.cloneKeyForOrganizations(decrypt(key.encryptedKey), orgs.orgs);
      }
    }

    if (requireModelCheck) {
      updates.modelIds = await this.getModelIds(key);
      updates.lastModelsCheckedAt = Date.now();
    }

    return { ...updates, nextCheckAt: null };
  }

  /**
   * Handle errors during key check according to annotated rules in comments.
   * Uses go-trycatch helper to minimize try/catch noise.
   */
  protected override async handleCheckingError(
    key: OpenAIKey,
    err: Error | CheckFailedError,
  ): Promise<Partial<OpenAIKey>> {
    if (err instanceof CheckFailedError) {
      const [parsed, parseErr] = await tryCatch(() => err.response.json() as Promise<OpenAIError>);
      const status = err.status;

      if (parseErr || !parsed || !parsed.error) {
        this.logger.error(
          { key: key.hash, error: err, parseErr },
          "Failed to parse error response while checking key. Scheduling recheck in ~60s.",
        );

        return { nextCheckAt: Date.now() + 60 * 1000 };
      }

      const apiErr = parsed.error;
      const type = apiErr?.type;

      if (status === 401) {
        this.logger.warn(
          { key: key.hash, error: parsed },
          "Key is invalid or revoked (401). Disabling key.",
        );

        return { status: "revoked", disabledBy: "key-checker", disabledAt: Date.now() };
      }

      if (status === 429) {
        switch (type) {
          case "insufficient_quota":
          case "billing_not_active":
          case "access_terminated": {
            const isRevoked = type === "access_terminated";

            this.logger.warn(
              { key: key.hash, rateLimitType: type, error: parsed },
              "Key returned a non-transient 429 error. Disabling key.",
            );

            return {
              status: isRevoked ? "revoked" : "out_of_quota",
              disabledBy: "key-checker",
              disabledAt: Date.now(),
            };
          }

          case "requests": {
            this.logger.warn(
              { key: key.hash, error: parsed },
              "Key is request-rate limited. Assuming operational; marking as checked.",
            );
            return {};
          }

          case "tokens": {
            this.logger.info(
              { key: key.hash },
              "Key is token-rate limited; treating as operational and marking as checked.",
            );
            return {};
          }

          default: {
            // Unknown 429 type: schedule a recheck soon rather than marking fully checked
            this.logger.error(
              { key: key.hash, rateLimitType: type, error: parsed },
              "Unexpected 429 error class while checking key. Scheduling recheck in ~60s.",
            );
            return { nextCheckAt: Date.now() + 60 * 1000 };
          }
        }
      }

      // Fallback for unexpected HTTP statuses:
      // Schedule a recheck after 60s using nextCheckAt.
      this.logger.error(
        { key: key.hash, status, error: parsed },
        "Unexpected error status while checking key. Will recheck in ~60s.",
      );
      return { nextCheckAt: Date.now() + 60 * 1000 };
    }

    // Network or generic error without HTTP status:
    // Schedule a recheck after 60s using nextCheckAt.
    this.logger.error(
      { key: key.hash, error: err.message },
      "Network error while checking key; retrying in ~60s.",
    );
    return { nextCheckAt: Date.now() + 60 * 1000 };
  }
}
