import { Hono, type Context } from "hono";

import { createProxy } from "@/lib/middlewares/create-proxy";
import { getGeminiSchema } from "@/lib/schemas/gemini";

import { createModelList } from "@/shared/utils/models";

const gemini = new Hono();

const geminiProxy = createProxy({
  provider: "gemini",
  basePath: "https://generativelanguage.googleapis.com",
  validateSchema: getGeminiSchema,
});

export async function generateGeminiModelList(ctx: Context) {
  const provider = ctx.var.keyPool.getKeyProvider("gemini");
  const avaiable = await provider.available();

  if (avaiable === 0) return [];

  const keys = await provider.list();
  const modelIds = Array.from(new Set(keys.map((k) => k.modelIds).flat()));

  return createModelList(modelIds, "gemini");
}

gemini.get("/:version{(v1|v1beta)}/models", async (ctx) => {
  const models = await generateGeminiModelList(ctx);
  return Response.json({ object: "list", data: models });
});

gemini.post(
  "/:version{(v1|v1beta)}/models/:path{(gemini-.*):(generateContent|streamGenerateContent)}",
  geminiProxy,
);

export { gemini };
