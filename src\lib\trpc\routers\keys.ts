import { sql } from "drizzle-orm";
import z from "zod";

import { schema } from "@/shared/database";
import { LLM_PROVIDERS } from "@/shared/providers";

import { protectedProcedure, t } from "..";

export const keysRouter = t.router({
  list: protectedProcedure
    .input(z.object({ provider: z.literal(LLM_PROVIDERS).optional() }))
    .query(({ ctx, input }) => {
      return ctx.db.query.keys.findMany({
        where: (table, { eq }) => (input.provider ? eq(table.provider, input.provider) : undefined),
        orderBy: (table, { desc }) => [desc(table.provider), desc(table.status)],
      });
    }),

  getByHash: protectedProcedure
    .input(z.object({ hash: z.string() }))
    .query(async ({ ctx, input }) => {
      const key = await ctx.db.query.keys.findFirst({
        where: (table, { eq }) => eq(table.hash, input.hash),
        with: { stats: { with: { modelFamily: true } } },
      });

      if (!key) throw new Error("Key not found");

      return key;
    }),

  disable: protectedProcedure
    .input(
      z.object({
        hash: z.string(),
        reason: z.string().min(1, "Reason is required"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const now = Math.floor(Date.now() / 1000);

      await ctx.db
        .update(schema.keys)
        .set({
          status: "disabled",
          disabledAt: now,
          disabledBy: "admin",
          metadata: sql`json_set(metadata, '$.disableReason', ${input.reason})`,
        })
        .where(sql`hash = ${input.hash}`);

      return { success: true };
    }),

  enable: protectedProcedure
    .input(z.object({ hash: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const data = await ctx.db
        .update(schema.keys)
        .set({
          status: "unknown", // Will be checked by key checker
          disabledAt: null,
          disabledBy: null,
          metadata: sql`json_remove(metadata, '$.disableReason', '$.pendingDeletion')`,
        })
        .where(sql`hash = ${input.hash}`)
        .returning({ provider: schema.keys.provider });

      await ctx.keysPool.recheckSingleKey(data[0]!.provider, input.hash);

      return { success: true };
    }),

  markForDeletion: protectedProcedure
    .input(z.object({ hash: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const deletionTime = Math.floor(Date.now() / 1000) + 24 * 60 * 60; // 24 hours from now

      await ctx.db
        .update(schema.keys)
        .set({
          metadata: sql`json_set(metadata, '$.pendingDeletion', ${deletionTime})`,
        })
        .where(sql`hash = ${input.hash} AND (status = 'disabled' OR status = 'revoked')`);

      return { success: true };
    }),

  cancelDeletion: protectedProcedure
    .input(z.object({ hash: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db
        .update(schema.keys)
        .set({
          metadata: sql`json_remove(metadata, '$.pendingDeletion')`,
        })
        .where(sql`hash = ${input.hash}`);

      return { success: true };
    }),

  addKeys: protectedProcedure
    .input(
      z.object({
        provider: z.enum(LLM_PROVIDERS),
        keys: z.string().min(1, "Keys string is required"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const keyList = input.keys
        .split(",")
        .map((key) => key.trim())
        .filter((key) => key.length > 0);

      if (keyList.length === 0) {
        throw new Error("No valid keys provided");
      }

      const addedCount = await ctx.keysPool.addKeys(input.provider, keyList);

      return {
        success: true,
        addedCount,
        totalKeys: keyList.length,
        message: `Added ${addedCount} out of ${keyList.length} keys`,
      };
    }),

  recheck: protectedProcedure
    .input(z.object({ provider: z.enum(LLM_PROVIDERS) }))
    .mutation(async ({ ctx, input }) => {
      await ctx.keysPool.recheck(input.provider);
      return { success: true };
    }),

  recheckKey: protectedProcedure
    .input(z.object({ hash: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Get the key to find its provider
      const key = await ctx.db.query.keys.findFirst({
        where: (table, { eq }) => eq(table.hash, input.hash),
      });

      if (!key) throw new Error("Key not found");

      // Recheck the specific provider (this will check all keys for that provider)
      await ctx.keysPool.recheckSingleKey(key.provider, key.hash);
      return { success: true };
    }),

  recheckAll: protectedProcedure.mutation(async ({ ctx }) => {
    await Promise.allSettled(LLM_PROVIDERS.map((provider) => ctx.keysPool.recheck(provider)));
    return { success: true };
  }),
});
