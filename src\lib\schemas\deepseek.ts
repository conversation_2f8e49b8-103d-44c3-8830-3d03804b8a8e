import type { Context } from "hono";

import { config } from "@/config";

import { OpenAIV1ChatCompletionSchema } from "./openai";

export function getDeepSeekSchema(ctx: Context) {
  return OpenAIV1ChatCompletionSchema.transform((data) => {
    if (!config.allowToolUsage.includes(ctx.var.service)) {
      delete data.tools;
      delete data.tool_choice;
      delete data.functions;
      delete data.function_choice;
    }

    /**
     * Reasoner models doesn't support temperature, top_p, presence_penalty, frequency_penalty, logprobs, top_logprobs.
     * See: https://api-docs.deepseek.com/guides/reasoning_model
     */
    if (!data.model.startsWith("reasoner")) {
      delete data.temperature;
      delete data.top_p;
      delete data.presence_penalty;
      delete data.frequency_penalty;

      delete data.logprobs;
      delete data.top_logprobs;
    }

    const max_completion_tokens =
      data.max_completion_tokens ?? data.max_tokens ?? Number.MAX_SAFE_INTEGER;

    // Should use max_completion_tokens instead of max_tokens as max_tokens is deprecated.
    // See: https://platform.openai.com/docs/api-reference/chat/create#chat-create-max_tokens
    delete data.max_tokens;
    data.max_completion_tokens = max_completion_tokens;

    if (!data.stream) delete data.stream_options;

    return data;
  });
}
