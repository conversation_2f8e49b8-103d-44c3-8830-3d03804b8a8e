import { text, integer } from "drizzle-orm/sqlite-core";
import { createId } from "@paralleldrive/cuid2";

import { sqliteTable } from "../helpers";
import { sql } from "drizzle-orm";

export const users = sqliteTable("users", {
  id: text()
    .primaryKey()
    .$defaultFn(() => createId()),

  username: text().notNull().unique(),
  passwordHash: text("password_hash").notNull(),

  createdAt: integer("created_at")
    .notNull()
    .default(sql`strftime('%s', 'now')`),
  updatedAt: integer("updated_at")
    .notNull()
    .default(sql`strftime('%s', 'now')`)
    .$onUpdateFn(() => sql`strftime('%s', 'now')`),

  metadata: text({ mode: "json" })
    .notNull()
    .$type<Record<string, string | number | boolean>>()
    .default({}),
});
