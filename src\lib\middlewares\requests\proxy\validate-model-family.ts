import type { Context } from "hono";

export async function validateModelFamily(ctx: Context) {
  const requestData = ctx.var.requestData!;

  const family = await ctx.var.db.query.modelFamilies.findFirst({
    where: (table, { eq, and, like }) =>
      and(eq(table.provider, requestData.provider), like(table.models, `%${requestData.model}%`)),
  });

  if (!family) throw new Error("No family found for model.");
  if (family.status === "disabled") throw new Error("This model family is currently disabled.");
}
