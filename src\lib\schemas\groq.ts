import type { Context } from "hono";
import { z } from "zod";

import { config } from "@/config";
import { OpenAIToolChoiceSchema, OpenAIToolsSchema } from "./openai";

const GroqContentSchema = z.union([
  z.string(),
  z.object({ type: z.literal("text"), text: z.string() }).array(),
]);

const GroqUserContentSchema = z.union([
  z.string(),
  z.object({ type: z.literal("text"), text: z.string() }).array(),
  z.object({ type: z.literal("image_url"), image_url: z.string() }).array(),
]);

export const GroqMessageSchema = z.discriminatedUnion("role", [
  z.object({ role: z.literal("user"), content: GroqUserContentSchema }),

  z.object({ role: z.literal("system"), content: GroqContentSchema }),
  z.object({ role: z.literal("tool"), content: Groq<PERSON>ontentSchema }),
  z.object({ role: z.literal("function"), content: GroqContentSchema }),

  z.object({
    role: z.literal("assistant"),
    content: GroqContentSchema.nullish(),
    reasoning: z.string(),
    tool_calls: z
      .object({
        id: z.string().optional(),
        type: z.literal("function").optional(),
        function: z.record(z.string(), z.unknown()).optional(),
      })
      .array()
      .optional(),
  }),
]);

export const GroqChatCompletionSchema = z.object({
  model: z.string(),
  messages: z.array(GroqMessageSchema),

  n: z.literal(1, { message: "You may only request a single completion at a time." }).optional(),

  /**
   * @deprecated use `max_completion_tokens` instead.
   */
  max_tokens: z.coerce.number().int().nullish(),
  max_completion_tokens: z.coerce.number().int().nullish(),

  logit_bias: z.any().optional(),

  frequency_penalty: z.number().min(-2).max(2).optional(),
  presence_penalty: z.number().min(-2).max(2).optional(),

  parallel_tool_calls: z.boolean().optional(),

  include_reasoning: z.boolean().nullish(),
  reasoning_effort: z.enum(["none", "default"]).nullish(),
  reasoning_format: z.literal(["hidden", "raw", "parsed"]).nullish(),
  response_format: z.record(z.string(), z.unknown()).optional(),

  search_settings: z
    .object({
      country: z.string().nullish(),
      exclude_domains: z.string().array().nullish(),
      include_domains: z.string().array().nullish(),
      include_images: z.boolean().nullish(),
    })
    .nullish(),

  seed: z.number().int().optional(),

  service_tier: z
    .literal(["auto", "on_demand", "flex", "performance", "null"])
    .default("on_demand"),

  stop: z.union([z.string(), z.array(z.string())]).nullish(),
  store: z.literal(false).default(false).nullish(),

  stream: z.boolean().optional().default(false),
  stream_options: z.object({ include_usage: z.boolean().optional() }).nullish(),

  temperature: z.number().optional(),

  tools: z.array(OpenAIToolsSchema).optional(),
  tool_choice: OpenAIToolChoiceSchema.optional(),

  logprobs: z.boolean().optional(),
  top_logprobs: z.number().int().optional(),

  top_p: z.number().optional(),
  user: z.string().optional(),
});

export function getGroqSchema(ctx: Context) {
  return GroqChatCompletionSchema.transform((data) => {
    if (!config.allowToolUsage.includes(ctx.var.service)) {
      delete data.tools;
      delete data.tool_choice;
    }

    const max_completion_tokens =
      data.max_completion_tokens ?? data.max_tokens ?? Number.MAX_SAFE_INTEGER;

    // Should use max_completion_tokens instead of max_tokens as max_tokens is deprecated.
    // See: https://platform.openai.com/docs/api-reference/chat/create#chat-create-max_tokens
    delete data.max_tokens;
    data.max_completion_tokens = max_completion_tokens;

    if (!data.stream) delete data.stream_options;

    /**
     * This field is only available for qwen3 models.
     * Set to 'none' to disable reasoning. Set to 'default' or null to let Qwen reason.
     * @see https://console.groq.com/docs/api-reference#chat-create
     */
    if (!data.model.includes("qwen3")) {
      delete data.reasoning_effort;
    }

    return data;
  });
}
