import chokidar from "chokidar";
import crypto from "crypto";
import fs from "fs";
import diff from "microdiff";
import path from "path";
import pino from "pino";

import { z } from "zod/v4";

import { LLM_PROVIDERS } from "./shared/providers";
import { normalizeForDiff, ObjectTyped } from "./shared/utils";

const isDev = process.env.NODE_ENV !== "production";
const startupLogger = pino({
  base: { pid: process.pid, module: "server" },
  transport: {
    target: "pino-pretty",
    options: {
      translateTime: "dd/mm/yy - HH:MM:ss",
      messageFormat: "{if module}\x1b[90m[{module}] \x1b[39m{end}{msg}",
      ignore: "module",
      colorize: true,
      singleLine: true,
    },
  },
});

function keysTransform(keys?: string | string[] | null) {
  if (!keys) return [];
  if (Array.isArray(keys)) return keys;
  return [...new Set(keys.split(",").map((k) => k.trim()))];
}

const configSchema = z.object({
  /**
   * The title of the server, displayed on the info page.
   * If not set, the title will be default to `AI-Reverse-Proxy`.
   */
  serverTitle: z.string().optional().default("AI-Reverse-Proxy"),
  /**
   * The port the proxy server will listen on.
   * If not set, the default is `7860`.
   */
  port: z.coerce.number().default(7860),
  /**
   * The network interface the proxy server will listen on.
   * If not set, the default is `0.0.0.0`.
   */
  bindAddress: z.string().default("0.0.0.0"),

  /**
   * The environment the proxy server is running in.
   * If not set, the default is `development`.
   *
   * Auto read from `process.env.NODE_ENV`.
   */
  enviroment: z
    .enum(["development", "production"])
    .default(process.env.NODE_ENV === "production" ? "production" : "development"),

  /**
   * The API keys to use for each service. You can pass your own environment variables here
   * or it will auto read from .env
   *
   * You can set the key to `null` to disable the service. Even if the environment variable is set,
   * it will be ignored if the key is set to `null` here.
   *
   * If not set, the default will be read from `process.env`.
   * - Openai: `process.env.OPENAI_KEYS`
   * - Anthropic: `process.env.ANTHROPIC_KEYS`
   * - Google: `process.env.GOOGLE_AI_KEYS`
   * - DeepSeek: `process.env.DEEPSEEK_KEYS`
   * - XAI: `process.env.XAI_KEYS`
   * - Groq: `process.env.GROQ_KEYS`
   */
  keys: z
    .object({
      /**
       * The OpenAI API keys to use. Pass `null` to disable OpenAI support.
       * If not set, the default is `process.env.OPENAI_KEYS`.
       */
      openai: z
        .union([z.string(), z.array(z.string())])
        .nullish()
        .default(process.env.OPENAI_KEYS ?? "")
        .transform(keysTransform),

      /**
       * The Anthropic API keys to use. Pass `null` to disable Anthropic support.
       * If not set, the default is `process.env.ANTHROPIC_KEYS`.
       */
      anthropic: z
        .union([z.string(), z.array(z.string())])
        .nullish()
        .default(process.env.ANTHROPIC_KEYS ?? "")
        .transform(keysTransform),

      /**
       * The Gemini API keys to use. Pass `null` to disable Gemini support.
       * If not set, the default is `process.env.GOOGLE_AI_KEYS`.
       */
      gemini: z
        .union([z.string(), z.array(z.string())])
        .nullish()
        .default(process.env.GOOGLE_AI_KEYS ?? "")
        .transform(keysTransform),

      /**
       * The DeepSeek API keys to use. Pass `null` to disable DeepSeek support.
       * If not set, the default is `process.env.DEEPSEEK_KEYS`.
       */
      deepseek: z
        .union([z.string(), z.array(z.string())])
        .nullish()
        .default(process.env.DEEPSEEK_KEYS ?? "")
        .transform(keysTransform),

      /**
       * The XAI API keys to use. Pass `null` to disable XAI support.
       * If not set, the default is `process.env.XAI_KEYS`.
       */
      xai: z
        .union([z.string(), z.array(z.string())])
        .nullish()
        .default(process.env.XAI_KEYS ?? "")
        .transform(keysTransform),

      /**
       * The Groq API keys to use. Pass `null` to disable Groq support.
       * If not set, the default is `process.env.GROQ_KEYS`.
       */
      groq: z
        .union([z.string(), z.array(z.string())])
        .nullish()
        .default(process.env.GROQ_KEYS ?? "")
        .transform(keysTransform),
    })
    .prefault({}),

  /**
   * List of disable providers.
   * If not set, the default is `[]`.
   */
  provider: z
    .enum(LLM_PROVIDERS)
    .array()
    .default([...LLM_PROVIDERS]),

  cookies: z
    .object({
      /**
       * Whether to use secure cookies.
       * Defaults to `true` in production and `false` in development.
       *
       * DO not use secure in local development.
       */
      secure: z.boolean().default(!isDev),

      /**
       * The SameSite attribute of the cookie.
       * Defaults to `strict`.
       */
      sameSite: z.enum(["strict", "lax", "none"]).default("strict"),

      /**
       * The maximum age of the cookie, in milliseconds.
       * Defaults to 14 days.
       */
      maxAge: z
        .number()
        .int()
        .min(0)
        .default(1000 * 60 * 60 * 24 * 14),

      /**
       * The signing key used to sign cookies and tokens.
       * If not set, a random key will be generated on startup.
       * It's recommended to set this explicitly.
       *
       * If not set, the default is `process.env.COOKIE_SECRET` or `process.env.SIGNING_KEY`, `process.env.SIGNING_KEY` take precedence.
       */
      signingKey: z
        .string()
        .optional()
        .overwrite((v) => v ?? process.env.COOKIE_SECRET ?? process.env.SIGNING_KEY),
    })
    .prefault({}),

  /**
   * URL of the Redis server if using the Redis store. The URL should be in the format `redis://username:password@host:port/db-number`.
   * If not set, the Redis store will be disabled and the memory store will be used.
   */
  redisUrl: z
    .string()
    .optional()
    .overwrite((v) => v ?? process.env.REDIS_URL),

  /**
   * Which user management mode to use.
   * - `none`: No user management. Proxy is open to all requests with basic abuse protection.
   * - `user_token`: Users must be created via by admins and provide their personal access token in the Authorization header to use the proxy. Configure this function and add users via the admin API or UI.
   */
  gatekeeper: z.enum(["none", "user_token"]).default("none"),

  database: z
    .discriminatedUnion("type", [
      z.object({
        /**
         * Persistence layer to use for user management.
         * - `sqlite`: Users are stored in a local SQLite database.
         * - `turso` : Users are stored in a cloud Turso database. https://turso.tech/
         */
        type: z.literal("sqlite"),

        /**
         * Path to the SQLite database.
         * @default "./data/database.sqlite"
         */
        path: z.string().default("./data/database.sqlite"),
      }),
      z.object({
        /**
         * Persistence layer to use for user management.
         * - `sqlite`: Users are stored in a local SQLite database.
         * - `turso` : Users are stored in a cloud Turso database. https://turso.tech/
         */
        type: z.literal("turso"),

        /**
         * Turso database URL.
         */
        url: z.string(),

        /**
         * Turso database auth token.
         * @default `process.env.TURSO_AUTH_TOKEN`
         */
        authToken: z.string().default(process.env.TURSO_AUTH_TOKEN ?? ""),
      }),
    ])
    .prefault({ type: "sqlite" }),

  /**
   * Maximum number of IPs allowed per user token.
   * Users with the manually-assigned `special` role are exempt from this limit.
   * - Defaults to 0, which means that users are not IP-limited.
   */
  maxIpsPerUser: z.coerce.number().int().min(0).default(0),
  /**
   * Whether a user token should be automatically disabled if it exceeds the `maxIpsPerUser` limit, or if only connections from new IPs are be rejected.
   */
  maxIpsAutoBan: z.boolean().default(false),

  /**
   * Whether to log events, such as generated completions, to the database.
   * Currently logged events include:
   *
   * - `chat-completion`: When a chat completion request is made.
   * - `new-ip`: When a new IP address is added to a user token.
   * - `user-action`: When a user action is performed, such as changing their nickname.
   */
  eventLogging: z.boolean().default(false),

  /**
   * Whether to publicly show total token costs on the info page.
   */
  showTokenCosts: z.boolean().default(false),

  /**
   * Whether to publicly show JSON info on the info page.
   */
  publicJsonInfo: z.boolean().default(false),

  /**
   * Whether to hash IP addresses before storing them in the database.
   * This is useful if you want to store IP addresses but don't want to expose them.
   */
  hashIp: z.boolean().default(false),

  /**
   * The message to display when a user is unauthorized.
   * Defaults to "Unauthorized access.".
   */
  unauthorizedMessage: z.string().default("Unauthorized access."),

  logging: z
    .object({
      /**
       * The verbosity level of diagnostic logging.
       * If not set, the default is `info`.
       */
      level: z.enum(["trace", "debug", "info", "warn", "error"]).default("info"),

      /**
       * Where to output logs.
       * - `console`: Log only to console
       * - `file`: Log only to files
       * - `both`: Log to both console and files (default)
       */
      output: z.enum(["console", "file", "both"]).default("both"),

      /**
       * Directory path for log files when file logging is enabled.
       * Log files will be created with date-based rotation:
       * - `default-YYYY-MM-DD.log` for info, warn, debug, trace logs
       * - `error-YYYY-MM-DD.log` for error and fatal logs
       *
       * If not set, the default is `./data/logging`.
       */
      directory: z.string().default("./data/logging"),
    })
    .prefault({}),

  /**
   * Whether to use a more minimal public Service Info page with static content.
   * Disables all stats pertaining to traffic, prompt/token usage, and queues.
   *
   * The full info page will appear if you have signed in as an admin using the
   * configured ADMIN_KEY and go to /admin/service-info.
   **/
  staticServiceInfo: z.boolean().default(false),

  /**
   * Whether to periodically check keys for usage and validity.
   */
  checkKeys: z.boolean().default(!isDev),

  /**
   * Whether to allow users to change their own nicknames via the UI.
   */
  allowNicknameChanges: z.boolean().default(true),

  /**
   * Trusted proxy hops. If you are deploying the server behind a reverse proxy
   * (Nginx, Cloudflare Tunnel, AWS WAF, etc.) the IP address of incoming
   * requests will be the IP address of the proxy, not the actual user.
   *
   * Depending on your hosting configuration, there may be multiple proxies/load
   * balancers between your server and the user. Each one will append the
   * incoming IP address to the `X-Forwarded-For` header. The user's real IP
   * address will be the first one in the list, assuming the header has not been
   * tampered with. Setting this value correctly ensures that the server doesn't
   * trust values in `X-Forwarded-For` not added by trusted proxies.
   *
   * In order for the server to determine the user's real IP address, you need
   * to tell it how many proxies are between the user and the server so it can
   * select the correct IP address from the `X-Forwarded-For` header.
   *
   * ! WARNING: If you set it incorrectly, the proxy will either record the
   * wrong IP address, or it will be possible for users to spoof their IP
   * addresses and bypass rate limiting. Check the request logs to see what
   * incoming X-Forwarded-For values look like.
   *
   * Examples:
   *  - X-Forwarded-For: "********, *********, ********" => trustedProxies: 3
   *  - X-Forwarded-For: "********" => trustedProxies: 1
   *  - no X-Forwarded-For header => trustedProxies: 0 (the actual IP of the incoming request will be used)
   *
   * As of 2024/01/08:
   * For HuggingFace or Cloudflare Tunnel, use 1.
   * For Render, use 3.
   * For deployments not behind a load balancer, use 0.
   *
   * You should double check against your actual request logs to be sure.
   * Defaults to 1, as most deployments are on HuggingFace or Cloudflare Tunnel.
   */
  trustedProxies: z.number().int().min(0).default(1),

  /**
   * Whether to allow tool usage. The proxy doesn't impelment any
   * support for tools/function calling but can pass requests and responses as is.
   *
   * Defaults to no services, meaning tool usage is disabled.
   *
   * Available services are: `openai`,`anthropic`,`google-ai`, `deepseek`
   */
  allowToolUsage: z.array(z.enum(LLM_PROVIDERS)).default([]),

  /**
   * Which services will accept prompts containing images, for use with
   * multimodal models. Users with `special` role are exempt from this
   * restriction.
   *
   * Do not enable this feature for untrusted users, as malicious users could
   * send images which violate your provider's terms of service or local laws.
   *
   * Defaults to no services, meaning image prompts are disabled.
   *
   * Available services are: `openai`,`anthropic`,`google-ai`, `deepseek`, `xai`
   * - For `deepseek`, vision is not supported so this setting has no effect.
   */
  allowedVisionServices: z.array(z.enum(LLM_PROVIDERS)).default([]),

  /**
   * Allows overriding the default proxy endpoint route. Defaults to `/proxy`.
   * A leading slash is required.
   */
  proxyEndpointRoute: z.string().default("/proxy"),

  /**
   * If set, only requests from these IP addresses will be permitted to use the
   * admin API and UI. Provide a comma-separated list of IP addresses or CIDR
   * ranges. If not set, the admin API and UI will be open to all requests.
   */
  adminWhitelist: z.array(z.string()).default(["0.0.0.0/0", "::/0"]),

  /**
   * If set, requests from these IP addresses will be blocked from using the
   * application. Provide a comma-separated list of IP addresses or CIDR ranges.
   * If not set, no IP addresses will be blocked.
   *
   * Takes precedence over the adminWhitelist.
   */
  ipBlacklist: z.array(z.string()).default([]),

  /**
   * The factor by which the tokens used in a request will be multiplied to determine the punishment time.
   * If not set, the default is 0.0.
   */
  tokensPunishmentFactor: z.number().min(0).default(0.0),

  /**
   * Default context limit for all models.
   * If not set, the default is 0 (no limit).
   */
  defaultGlobalMaxContext: z
    .number()
    .int()
    .min(0)
    .default(0)
    .transform((v) => (v === 0 ? Number.MAX_SAFE_INTEGER : v)),

  /**
   * Default output limit for all models.
   * If not set, the default is 0 (no limit).
   */
  defaultGlobalMaxOutput: z
    .number()
    .int()
    .min(0)
    .default(0)
    .transform((v) => (v === 0 ? Number.MAX_SAFE_INTEGER : v)),

  /**
   * Default rate limit for all models.
   * If not set, the default is 10.
   */
  ratelimitConfig: z
    .object({
      /**
       * The maximum number of tokens that can be used in a given time period.
       * Defaults to 10.
       */
      capacity: z.number().int().min(0).default(10),

      /**
       * The number of tokens that are added to the bucket every `refillInterval` milliseconds.
       * Defaults to 1.
       */
      refillRate: z.number().min(0).default(1),

      /**
       * The time interval (in milliseconds) at which the token bucket is refilled.
       * Defaults to 1000ms (1 token per second).
       */
      refillInterval: z.number().int().min(1).default(5000),
    })
    .prefault({}),

  /**
   * Model-specific rate limits, context limits, and output limits.
   * If not set, the default to the global limits.
   */
  // modelFamilySettings: z
  //   .partialRecord(
  //     z.enum(MODEL_FAMILIES),
  //     z.object({
  //       weight: z.number().min(0).default(1),
  //       maxContext: z.number().min(0).default(Number.MAX_SAFE_INTEGER),
  //       maxOutput: z.number().min(0).default(Number.MAX_SAFE_INTEGER),
  //     }),
  //   )
  //   .prefault({})
  //   .transform((data) => new Map(ObjectTyped.entries(data))),

  /**
   * List of models that are not allowed to be used.
   * Support regex or partial match.
   *
   * Default disallow model: `o1-pro`, `claude-3-opus`, `gpt-4.5-preview`
   */
  disallowedModels: z
    .array(z.union([z.string(), z.instanceof(RegExp)]))
    .default(["o1-pro", "claude-3-opus", "gpt-4.5-preview"]),

  block: z
    .object({
      /**
       * Comma-separated list of origins to block. Requests matching any of these
       * origins or referers will be rejected.
       *
       * - Partial matches are allowed, so `reddit` will match `www.reddit.com`.
       * - Include only the hostname, not the protocol or path, e.g:
       *  `reddit.com,9gag.com,gaiaonline.com`
       */
      origins: z.array(z.string()).optional(),

      /** Message to return when rejecting requests from blocked origins. */
      message: z.string().optional().default("Access denied."),

      /**
       * Destination URL to redirect blocked requests to, for non-JSON requests.
       */
      redirect: z.string().optional(),
    })
    .optional(),

  /**
   * Enable experimental features. This may contain breaking changes.
   * Use at your own risk.
   *
   * List of experimental features:
   */
  experimentals: z.object({}).optional(),
});

type Config = z.infer<typeof configSchema>;

export function defineConfig(config: z.input<typeof configSchema>) {
  const result = configSchema.safeParse(config);

  if (!result.success) {
    throw Error("Config validation failed: " + result.error.message, { cause: result.error });
  }

  return result.data;
}

function loadCustomConfig() {
  const defaultConfig = configSchema.parse({});
  const customConfigPath = path.join(__dirname, "..", "proxy.config.ts");

  if (!fs.existsSync(customConfigPath)) return defaultConfig;
  const customConfig = require(customConfigPath).default as ReturnType<typeof defineConfig>;

  return { ...defaultConfig, ...customConfig };
}

/**
 * To change configs, create a file called `proxy.config.ts` in the root directory.
 *
 * See `.env.example` and `README.md` for more information.
 */
export const config: Config = loadCustomConfig();

const isServer = process.argv[1]?.includes("server.ts");
if (isServer && process.env.NODE_ENV === "production") {
  const watcher = chokidar.watch("./proxy.config.ts", {
    persistent: true,
    ignoreInitial: true,
    awaitWriteFinish: true,
  });

  startupLogger.info("Watching for `proxy.config.ts` changes...");

  watcher.on("error", (error) => {
    startupLogger.error(error, "Error watching file:");
  });

  watcher.on("change", (path) => {
    delete require.cache[require.resolve(path)];
    const newConfig = loadCustomConfig();

    const patches = diff(normalizeForDiff(config), normalizeForDiff(newConfig));
    const topKeys = new Set(patches.map((p) => String(p.path[0]) as keyof Config));

    if (topKeys.has("keys")) {
      startupLogger.warn(
        "Config changed, but updating service api keys is not supported. Please use Admin UI to update keys.",
      );
      topKeys.delete("keys");
    }

    const dangerous = DANGEROUS_KEYS.filter((k) => topKeys.has(k));
    if (dangerous.length) {
      startupLogger.warn(
        { changedKeys: Array.from(topKeys), dangerousChanges: dangerous },
        "Dangerous config key(s) changed - please restart the server manually to apply these changes and avoid undefined behavior.",
      );

      if (dangerous.length === topKeys.size) return;

      for (const key of dangerous) topKeys.delete(key);
      startupLogger.info(
        { changedKeys: Array.from(topKeys), dangerousChanges: dangerous },
        "Non-dangerous config key(s) changed. Removed dangerous key changes, continued applying changes...",
      );
    }

    if (!topKeys.size) {
      startupLogger.info("No config changes detected.");
      return;
    }

    startupLogger.info(
      { changed: patches.map((p) => p.path.join(".")) },
      "Applying config changes...",
    );

    Object.assign(config, newConfig);
    startupLogger.info("Config changes applied.");
  });
}

function generateSigningKey() {
  const secrets = [
    config.keys.openai,
    config.keys.anthropic,
    config.keys.gemini,
    config.keys.deepseek,
    config.keys.xai,
    config.keys.groq,
  ];

  if (secrets.filter((s) => s).length === 0) {
    startupLogger.warn(
      "No SIGNING_KEY or secrets are set. All sessions, cookies, and proofs of work will be invalidated on restart.",
    );
    return crypto.randomBytes(32).toString("hex");
  }

  startupLogger.info("No SIGNING_KEY set; one will be generated from secrets.");
  startupLogger.warn(
    "It's recommended to set SIGNING_KEY explicitly to ensure users' sessions and cookies always persist across restarts.",
  );
  const seed = secrets.map((s) => (Array.isArray(s) ? s.join("") : s) || "n/a").join("");
  return crypto.createHash("sha256").update(seed).digest("hex");
}

export const SECRET_SIGNING_KEY = config.cookies.signingKey ?? generateSigningKey();

/**
 * Config keys that are not suppose to changes after the server starts.
 * If changed, could cause unexpected behavior or errors.
 */
export const DANGEROUS_KEYS: (keyof Config)[] = [
  "port",
  "keys",
  "logging",
  "checkKeys",
  "bindAddress",
  "trustedProxies",
  "database",
  "proxyEndpointRoute",
  "cookies",
  "redisUrl",
  "gatekeeper",
];
